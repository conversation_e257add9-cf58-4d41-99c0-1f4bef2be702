// 智能网页总结助手 - 内容脚本
// 负责从网页中提取主要文本内容

class ContentExtractor {
  constructor() {
    this.initializeExtractor();
  }

  // 初始化内容提取器
  initializeExtractor() {
    // 监听来自background script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'extractContent') {
        try {
          const content = this.extractPageContent();
          sendResponse({ success: true, content: content });
        } catch (error) {
          console.error('内容提取失败:', error);
          sendResponse({ success: false, error: error.message });
        }
      }
      return true;
    });

    // 页面加载完成后自动提取内容（可选）
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.cachePageContent();
      });
    } else {
      this.cachePageContent();
    }
  }

  // 提取页面主要内容
  extractPageContent() {
    console.log('开始提取页面内容...');
    
    // 创建页面内容的副本以避免修改原页面
    const pageClone = document.cloneNode(true);
    
    // 移除不需要的元素
    this.removeUnwantedElements(pageClone);
    
    // 查找主要内容区域
    const mainContent = this.findMainContent(pageClone);
    
    // 提取和清理文本
    const textContent = this.extractAndCleanText(mainContent);
    
    // 分析内容结构
    const contentAnalysis = this.analyzeContent(mainContent);
    
    // 同时提取Markdown格式的内容
    const markdownContent = this.extractToMarkdown(mainContent);

    const result = {
      content: textContent,
      markdown: markdownContent,
      title: document.title,
      url: window.location.href,
      wordCount: this.countWords(textContent),
      markdownWordCount: this.countWords(markdownContent),
      language: this.detectLanguage(textContent),
      structure: contentAnalysis,
      extractedAt: new Date().toISOString()
    };
    
    console.log('内容提取完成:', result);
    return result;
  }

  // 移除不需要的元素
  removeUnwantedElements(doc) {
    const unwantedSelectors = [
      // 脚本和样式
      'script', 'style', 'noscript',
      
      // 导航和菜单
      'nav', 'header', 'footer', 'aside',
      '.navigation', '.nav', '.menu', '.sidebar',
      '.breadcrumb', '.breadcrumbs',
      
      // 广告和推广
      '.advertisement', '.ads', '.ad', '.advert',
      '.sponsored', '.promotion', '.banner',
      '[class*="ad-"]', '[id*="ad-"]',
      '[class*="ads-"]', '[id*="ads-"]',
      
      // 社交和分享
      '.social', '.social-share', '.share-buttons',
      '.social-media', '.follow-us',
      
      // 评论和互动
      '.comments', '.comment', '.discussion',
      '.reviews', '.rating', '.feedback',
      
      // 弹窗和模态框
      '.popup', '.modal', '.overlay', '.lightbox',
      '.newsletter', '.subscription',
      
      // 相关内容和推荐
      '.related', '.recommended', '.suggestions',
      '.more-stories', '.you-might-like',
      
      // 工具栏和控件
      '.toolbar', '.controls', '.player-controls',
      '.video-controls', '.audio-controls',
      
      // 其他干扰元素
      '.cookie-notice', '.gdpr-notice',
      '.loading', '.spinner', '.placeholder',
      'iframe', 'embed', 'object'
    ];

    unwantedSelectors.forEach(selector => {
      try {
        const elements = doc.querySelectorAll(selector);
        elements.forEach(element => {
          if (element && element.parentNode) {
            element.parentNode.removeChild(element);
          }
        });
      } catch (error) {
        console.warn(`移除元素失败: ${selector}`, error);
      }
    });

    // 移除隐藏元素
    this.removeHiddenElements(doc);
    
    // 移除空元素
    this.removeEmptyElements(doc);
  }

  // 移除隐藏元素
  removeHiddenElements(doc) {
    const allElements = doc.querySelectorAll('*');
    allElements.forEach(element => {
      try {
        const style = window.getComputedStyle(element);
        if (style.display === 'none' || 
            style.visibility === 'hidden' || 
            style.opacity === '0' ||
            element.hidden) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }
      } catch (error) {
        // 忽略样式获取错误
      }
    });
  }

  // 移除空元素
  removeEmptyElements(doc) {
    const emptyElements = doc.querySelectorAll('div, span, p, section, article');
    emptyElements.forEach(element => {
      if (element.textContent.trim() === '' && 
          element.children.length === 0) {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      }
    });
  }

  // 查找主要内容区域
  findMainContent(doc) {
    // 按优先级尝试不同的选择器
    const contentSelectors = [
      // 语义化标签
      'main',
      'article',
      '[role="main"]',
      
      // 常见的内容类名
      '.content',
      '.main-content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '.page-content',
      
      // 新闻网站常用
      '.story-body',
      '.article-body',
      '.post-body',
      '.news-content',
      
      // 博客常用
      '.post',
      '.entry',
      '.blog-post',
      
      // 通用ID
      '#content',
      '#main',
      '#main-content',
      '#article',
      '#post'
    ];

    for (const selector of contentSelectors) {
      const element = doc.querySelector(selector);
      if (element && this.hasSignificantContent(element)) {
        console.log(`找到主要内容区域: ${selector}`);
        return element;
      }
    }

    // 如果没找到特定的内容区域，使用body但进一步过滤
    console.log('使用body作为内容区域');
    return this.filterBodyContent(doc.body || doc.documentElement);
  }

  // 检查元素是否包含有意义的内容
  hasSignificantContent(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    return wordCount > 50; // 至少50个词
  }

  // 过滤body内容
  filterBodyContent(body) {
    // 创建一个新的容器
    const filteredContent = document.createElement('div');
    
    // 查找所有可能包含主要内容的元素
    const contentElements = body.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote, pre');
    
    contentElements.forEach(element => {
      if (this.isContentElement(element)) {
        filteredContent.appendChild(element.cloneNode(true));
      }
    });
    
    return filteredContent;
  }

  // 判断是否为内容元素
  isContentElement(element) {
    const text = element.textContent || '';
    const wordCount = this.countWords(text);
    
    // 过滤太短的文本
    if (wordCount < 3) return false;
    
    // 过滤导航链接
    if (element.tagName === 'A' && wordCount < 10) return false;
    
    // 过滤可能的菜单项
    const className = element.className || '';
    const id = element.id || '';
    const unwantedPatterns = ['menu', 'nav', 'header', 'footer', 'sidebar', 'ad'];
    
    for (const pattern of unwantedPatterns) {
      if (className.toLowerCase().includes(pattern) || 
          id.toLowerCase().includes(pattern)) {
        return false;
      }
    }
    
    return true;
  }

  // 提取和清理文本
  extractAndCleanText(element) {
    let text = element.textContent || element.innerText || '';

    // 清理文本
    text = text
      // 移除多余的空白字符
      .replace(/\s+/g, ' ')
      // 移除多余的换行
      .replace(/\n\s*\n/g, '\n')
      // 移除行首行尾空白
      .replace(/^\s+|\s+$/gm, '')
      // 移除特殊字符（保留基本标点）
      .replace(/[^\w\s\u4e00-\u9fff.,!?;:()[\]{}""''—–-]/g, '')
      .trim();

    // 限制内容长度（避免超过API限制）
    const maxLength = 10000;
    if (text.length > maxLength) {
      text = text.substring(0, maxLength) + '...';
      console.log(`内容过长，已截断至${maxLength}字符`);
    }

    return text;
  }

  // 提取HTML结构并转换为Markdown
  extractToMarkdown(element) {
    console.log('开始提取HTML结构并转换为Markdown...');

    // 创建HTML到Markdown的转换器
    const converter = new HTMLToMarkdownConverter();

    // 转换为Markdown
    const markdown = converter.convert(element);

    // 限制内容长度
    const maxLength = 15000; // Markdown格式需要更多空间
    if (markdown.length > maxLength) {
      const truncated = markdown.substring(0, maxLength);
      // 尝试在合适的位置截断（避免截断到标记中间）
      const lastNewline = truncated.lastIndexOf('\n\n');
      const finalMarkdown = lastNewline > maxLength * 0.8 ?
        truncated.substring(0, lastNewline) + '\n\n...' :
        truncated + '...';
      console.log(`Markdown内容过长，已截断至${finalMarkdown.length}字符`);
      return finalMarkdown;
    }

    return markdown;
  }

  // 分析内容结构
  analyzeContent(element) {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const paragraphs = element.querySelectorAll('p');
    const lists = element.querySelectorAll('ul, ol');
    const images = element.querySelectorAll('img');
    
    return {
      headingCount: headings.length,
      paragraphCount: paragraphs.length,
      listCount: lists.length,
      imageCount: images.length,
      hasStructure: headings.length > 0
    };
  }

  // 统计词数
  countWords(text) {
    if (!text) return 0;
    
    // 中文字符按字符计算，英文按单词计算
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').match(/\b\w+\b/g);
    const englishWordCount = englishWords ? englishWords.length : 0;
    
    return chineseChars + englishWordCount;
  }

  // 检测语言
  detectLanguage(text) {
    if (!text) return 'unknown';
    
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const totalChars = text.length;
    
    if (chineseChars / totalChars > 0.3) {
      return 'zh';
    } else {
      return 'en';
    }
  }

  // 缓存页面内容（可选功能）
  cachePageContent() {
    try {
      const content = this.extractPageContent();
      // 可以将内容缓存到localStorage或发送给background script
      console.log('页面内容已缓存');
    } catch (error) {
      console.error('缓存页面内容失败:', error);
    }
  }
}

// HTML到Markdown转换器
class HTMLToMarkdownConverter {
  constructor() {
    this.imageCounter = 0;
    this.linkReferences = new Map();
    this.referenceCounter = 0;
  }

  // 主转换方法
  convert(element) {
    if (!element) return '';

    // 克隆元素避免修改原始DOM
    const clonedElement = element.cloneNode(true);

    // 预处理：清理和标准化HTML
    this.preprocessHTML(clonedElement);

    // 转换为Markdown
    const markdown = this.convertElement(clonedElement);

    // 后处理：清理和格式化
    return this.postprocessMarkdown(markdown);
  }

  // 预处理HTML
  preprocessHTML(element) {
    // 移除脚本和样式标签
    const unwantedTags = element.querySelectorAll('script, style, noscript');
    unwantedTags.forEach(tag => tag.remove());

    // 标准化空白字符
    this.normalizeWhitespace(element);

    // 处理嵌套的格式标签
    this.flattenNestedFormatting(element);
  }

  // 标准化空白字符
  normalizeWhitespace(element) {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    textNodes.forEach(textNode => {
      // 保留段落间的换行，但清理多余空白
      textNode.textContent = textNode.textContent
        .replace(/\s+/g, ' ')
        .replace(/^\s+|\s+$/g, '');
    });
  }

  // 扁平化嵌套的格式标签
  flattenNestedFormatting(element) {
    // 处理嵌套的strong/b标签
    const strongTags = element.querySelectorAll('strong strong, b b, strong b, b strong');
    strongTags.forEach(tag => {
      const parent = tag.parentNode;
      while (tag.firstChild) {
        parent.insertBefore(tag.firstChild, tag);
      }
      tag.remove();
    });

    // 处理嵌套的em/i标签
    const emTags = element.querySelectorAll('em em, i i, em i, i em');
    emTags.forEach(tag => {
      const parent = tag.parentNode;
      while (tag.firstChild) {
        parent.insertBefore(tag.firstChild, tag);
      }
      tag.remove();
    });
  }

  // 转换元素
  convertElement(element) {
    if (!element) return '';

    const tagName = element.tagName ? element.tagName.toLowerCase() : '';

    // 处理文本节点
    if (element.nodeType === Node.TEXT_NODE) {
      return this.escapeMarkdown(element.textContent || '');
    }

    // 处理不同的HTML标签
    switch (tagName) {
      case 'h1': case 'h2': case 'h3': case 'h4': case 'h5': case 'h6':
        return this.convertHeading(element);
      case 'p':
        return this.convertParagraph(element);
      case 'br':
        return '\n';
      case 'strong': case 'b':
        return this.convertStrong(element);
      case 'em': case 'i':
        return this.convertEmphasis(element);
      case 'del': case 's': case 'strike':
        return this.convertStrikethrough(element);
      case 'code':
        return this.convertInlineCode(element);
      case 'pre':
        return this.convertCodeBlock(element);
      case 'a':
        return this.convertLink(element);
      case 'img':
        return this.convertImage(element);
      case 'ul': case 'ol':
        return this.convertList(element);
      case 'li':
        return this.convertListItem(element);
      case 'blockquote':
        return this.convertBlockquote(element);
      case 'table':
        return this.convertTable(element);
      case 'hr':
        return '\n\n---\n\n';
      case 'div': case 'section': case 'article': case 'main':
        return this.convertContainer(element);
      default:
        return this.convertContainer(element);
    }
  }

  // 转换标题
  convertHeading(element) {
    const level = parseInt(element.tagName.charAt(1));
    const prefix = '#'.repeat(level);
    const content = this.convertChildren(element);
    return `\n\n${prefix} ${content.trim()}\n\n`;
  }

  // 转换段落
  convertParagraph(element) {
    const content = this.convertChildren(element);
    return content.trim() ? `\n\n${content.trim()}\n\n` : '';
  }

  // 转换粗体
  convertStrong(element) {
    const content = this.convertChildren(element);
    return content.trim() ? `**${content.trim()}**` : '';
  }

  // 转换斜体
  convertEmphasis(element) {
    const content = this.convertChildren(element);
    return content.trim() ? `*${content.trim()}*` : '';
  }

  // 转换删除线
  convertStrikethrough(element) {
    const content = this.convertChildren(element);
    return content.trim() ? `~~${content.trim()}~~` : '';
  }

  // 转换行内代码
  convertInlineCode(element) {
    const content = element.textContent || '';
    return content ? `\`${content}\`` : '';
  }

  // 转换代码块
  convertCodeBlock(element) {
    const codeElement = element.querySelector('code');
    const content = codeElement ? codeElement.textContent : element.textContent;
    const language = this.extractLanguage(codeElement);

    return `\n\n\`\`\`${language}\n${content || ''}\n\`\`\`\n\n`;
  }

  // 提取代码语言
  extractLanguage(codeElement) {
    if (!codeElement) return '';

    const className = codeElement.className || '';
    const languageMatch = className.match(/language-(\w+)/);
    return languageMatch ? languageMatch[1] : '';
  }

  // 转换链接
  convertLink(element) {
    const href = element.getAttribute('href') || '';
    const content = this.convertChildren(element);

    if (!href || !content.trim()) {
      return content;
    }

    // 检查是否是相对链接，如果是则转换为绝对链接
    const absoluteHref = this.makeAbsoluteUrl(href);

    return `[${content.trim()}](${absoluteHref})`;
  }

  // 转换图片
  convertImage(element) {
    const src = element.getAttribute('src') || '';
    const alt = element.getAttribute('alt') || '';
    const title = element.getAttribute('title') || '';

    if (!src) return '';

    // 转换为绝对URL
    const absoluteSrc = this.makeAbsoluteUrl(src);

    // 构建Markdown图片语法
    let markdown = `![${alt}](${absoluteSrc}`;
    if (title) {
      markdown += ` "${title}"`;
    }
    markdown += ')';

    return markdown;
  }

  // 转换列表
  convertList(element) {
    const isOrdered = element.tagName.toLowerCase() === 'ol';
    const items = Array.from(element.children).filter(child =>
      child.tagName && child.tagName.toLowerCase() === 'li'
    );

    if (items.length === 0) return '';

    let result = '\n\n';
    items.forEach((item, index) => {
      const content = this.convertListItem(item, isOrdered, index + 1);
      result += content;
    });
    result += '\n';

    return result;
  }

  // 转换列表项
  convertListItem(element, isOrdered = false, index = 1) {
    const content = this.convertChildren(element);
    const trimmedContent = content.trim();

    if (!trimmedContent) return '';

    const prefix = isOrdered ? `${index}. ` : '- ';

    // 处理多行内容的缩进
    const lines = trimmedContent.split('\n');
    const formattedLines = lines.map((line, lineIndex) => {
      if (lineIndex === 0) {
        return `${prefix}${line}`;
      } else {
        // 后续行需要缩进对齐
        const indent = ' '.repeat(prefix.length);
        return `${indent}${line}`;
      }
    });

    return formattedLines.join('\n') + '\n';
  }

  // 转换引用块
  convertBlockquote(element) {
    const content = this.convertChildren(element);
    const lines = content.trim().split('\n');

    const quotedLines = lines.map(line => `> ${line.trim()}`);
    return `\n\n${quotedLines.join('\n')}\n\n`;
  }

  // 转换表格
  convertTable(element) {
    const rows = Array.from(element.querySelectorAll('tr'));
    if (rows.length === 0) return '';

    let markdown = '\n\n';

    rows.forEach((row, rowIndex) => {
      const cells = Array.from(row.querySelectorAll('td, th'));
      const cellContents = cells.map(cell => {
        const content = this.convertChildren(cell);
        // 移除表格单元格内的换行，用空格替换
        return content.replace(/\n/g, ' ').trim();
      });

      // 构建表格行
      markdown += `| ${cellContents.join(' | ')} |\n`;

      // 如果是第一行（表头），添加分隔线
      if (rowIndex === 0) {
        const separators = cells.map(() => '---');
        markdown += `| ${separators.join(' | ')} |\n`;
      }
    });

    markdown += '\n';
    return markdown;
  }

  // 转换容器元素
  convertContainer(element) {
    return this.convertChildren(element);
  }

  // 转换子元素
  convertChildren(element) {
    if (!element || !element.childNodes) return '';

    let result = '';
    for (const child of element.childNodes) {
      result += this.convertElement(child);
    }

    return result;
  }

  // 转换为绝对URL
  makeAbsoluteUrl(url) {
    if (!url) return '';

    // 如果已经是绝对URL，直接返回
    if (url.match(/^https?:\/\//)) {
      return url;
    }

    // 如果是相对URL，转换为绝对URL
    try {
      const baseUrl = window.location.origin;
      return new URL(url, baseUrl).href;
    } catch (error) {
      console.warn('无法转换URL:', url, error);
      return url;
    }
  }

  // 转义Markdown特殊字符
  escapeMarkdown(text) {
    if (!text) return '';

    // 转义Markdown特殊字符，但保留基本格式
    return text
      .replace(/\\/g, '\\\\')
      .replace(/\*/g, '\\*')
      .replace(/_/g, '\\_')
      .replace(/`/g, '\\`')
      .replace(/\[/g, '\\[')
      .replace(/\]/g, '\\]')
      .replace(/\(/g, '\\(')
      .replace(/\)/g, '\\)')
      .replace(/#/g, '\\#')
      .replace(/\+/g, '\\+')
      .replace(/-/g, '\\-')
      .replace(/\./g, '\\.')
      .replace(/!/g, '\\!');
  }

  // 后处理Markdown
  postprocessMarkdown(markdown) {
    if (!markdown) return '';

    return markdown
      // 清理多余的空行
      .replace(/\n{4,}/g, '\n\n\n')
      // 清理行尾空格
      .replace(/[ \t]+$/gm, '')
      // 清理开头和结尾的空行
      .replace(/^\n+/, '')
      .replace(/\n+$/, '\n')
      // 确保列表前后有空行
      .replace(/(\n\n)([-*+]|\d+\.)\s/g, '$1$2 ')
      // 确保标题前后有空行
      .replace(/(\n\n)(#{1,6})\s/g, '$1$2 ')
      .trim();
  }
}

// 初始化内容提取器
if (typeof window !== 'undefined') {
  new ContentExtractor();
}
